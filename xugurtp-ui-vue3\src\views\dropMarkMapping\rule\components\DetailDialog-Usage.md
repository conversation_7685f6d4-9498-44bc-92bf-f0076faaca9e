# DetailDialog 通用组件使用说明

DetailDialog 是一个高度集成的通用详情弹窗组件，通过简单的配置即可在多个场景中复用。

## 功能特性

- **统一组件架构**：一个组件支持多种显示模式，无需额外的子组件
- **行内折叠表格**：支持表格行点击展开详细信息
- **智能配置**：根据类型自动适配表格列和展开内容
- **数据驱动**：通过数据对象传递显示内容
- **Element Plus 风格**：使用 el-descriptions 组件展示基础信息，风格统一

## 支持的显示类型

### 1. rule - 规则明细模式

用于显示评估规则的详细信息，包括规则名称、基础信息描述列表和可展开的规则明细表格。

### 2. execute - 执行记录模式

用于显示落标执行记录的详细信息，包括规则信息、基础信息描述列表和有效映射关系表格。

## 使用方法

### 1. 导入组件

```vue
<script setup>
import DetailDialog from '@/views/dropMarkMapping/rule/components/DetailDialog.vue'
</script>
```

### 2. 在模板中使用

```vue
<template>
  <!-- 规则明细弹窗 -->
  <DetailDialog
    v-model="ruleDetailDialog.visible"
    :data="ruleDetailDialogData"
    :config="ruleDetailDialogConfig"
  />
  
  <!-- 执行记录明细弹窗 -->
  <DetailDialog
    v-model="executeRecordDialog.visible"
    :data="executeRecordDialogData"
    :config="executeRecordDialogConfig"
  />
</template>
```

### 3. 配置数据结构

#### 规则明细配置示例

```javascript
// 弹窗状态
const ruleDetailDialog = reactive({
  visible: false
})

// 弹窗配置
const ruleDetailDialogConfig = reactive({
  type: 'rule',                    // 类型：rule
  title: '规则明细',
  width: '1400px',
  closeText: '关闭',
  sectionTitle: '规则明细'
})

// 弹窗数据
const ruleDetailDialogData = reactive({
  ruleName: '我是规则名称',
  basicInfo: {
    ruleName: '规则名称',
    dataStandard: '数据标准',
    standardSet: '基础标准集',
    mappingObject: 'dws_dddd_dddddd'
  },
  tableData: [
    {
      id: 1,
      standardAttribute: '员工ID',
      evaluationResult: '不通过',
      standardValue: 'STRING',
      objectValue: 'varchar(64)',
      ruleType: '元数据'
    }
  ]
})
```

#### 执行记录配置示例

```javascript
// 弹窗状态
const executeRecordDialog = reactive({
  visible: false
})

// 弹窗配置
const executeRecordDialogConfig = reactive({
  type: 'execute',                 // 类型：execute
  title: '落标明细',
  width: '1400px',
  closeText: '关闭',
  sectionTitle: '落标执行记录'
})

// 弹窗数据
const executeRecordDialogData = reactive({
  ruleName: '金融标准映射规则',
  basicInfo: {
    ruleName: '金融标准映射规则',
    mappingDataset: '落标数据集（公示标准版/基础）：性别_姓名_同工同',
    startTime: '2025-05-05 10:47:44',
    belongAsset: 'dws_dddd_dddddd'
  },
  tableData: [
    {
      id: 1,
      standardChineseName: '员工ID',
      standardCode: 'USERID',
      validMappingCount: 4,
      mappingRelations: [
        { metadataCode: 'user_id', metadataPath: '111/222/表名' }
      ]
    }
  ]
})
```

### 4. 打开弹窗

```javascript
const handleViewRuleDetail = (row) => {
  ruleDetailDialog.visible = true
  
  // 设置弹窗数据
  Object.assign(ruleDetailDialogData, {
    ruleName: '我是规则名称',
    basicInfo: {
      ruleName: '规则名称',
      dataStandard: '数据标准',
      standardSet: '基础标准集',
      mappingObject: row.belongAsset || 'dws_dddd_dddddd'
    },
    tableData: mockRuleDetailData
  })
}

const handleViewExecuteRecord = (row) => {
  executeRecordDialog.visible = true
  
  // 设置弹窗数据
  Object.assign(executeRecordDialogData, {
    ruleName: '金融标准映射规则',
    basicInfo: {
      ruleName: '金融标准映射规则',
      mappingDataset: '落标数据集（公示标准版/基础）：性别_姓名_同工同',
      startTime: row.startTime || '2025-05-05 10:47:44',
      belongAsset: 'dws_dddd_dddddd'
    },
    tableData: mockExecuteRecordData
  })
}
```

## 配置参数说明

### config 配置对象

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| type | String | 'rule' | 显示类型，可选值：'rule', 'execute' |
| title | String | '详情' | 弹窗标题 |
| width | String | '1400px' | 弹窗宽度 |
| closeText | String | '关闭' | 关闭按钮文本 |
| sectionTitle | String | '明细信息' | 主要部分标题 |

### data 数据对象

#### rule 类型数据结构

```javascript
{
  ruleName: String,           // 规则名称
  basicInfo: {
    ruleName: String,         // 规则名称
    dataStandard: String,     // 数据标准
    standardSet: String,      // 标准集
    mappingObject: String     // 映射对象
  },
  tableData: Array           // 表格数据，包含：standardAttribute, evaluationResult, standardValue, objectValue, ruleType
}
```

#### execute 类型数据结构

```javascript
{
  ruleName: String,           // 规则名称
  basicInfo: {
    ruleName: String,         // 规则名称
    mappingDataset: String,   // 映射数据集
    startTime: String,        // 开始执行时间
    belongAsset: String       // 所属资产
  },
  tableData: Array           // 表格数据，包含：standardChineseName, standardCode, validMappingCount, mappingRelations
}
```

## 扩展新的显示类型

如果需要添加新的显示类型，可以按照以下步骤：

1. 在 `DetailDialog.vue` 的 `infoCards` 计算属性中添加新类型的卡片配置
2. 在 `tableColumns` 计算属性中添加新类型的表格列配置
3. 在模板的展开内容部分添加新类型的展开内容结构

```javascript
// 在 infoCards 计算属性中添加
if (props.config.type === 'newType') {
  return [
    { label: '字段1', value: basicInfo.field1 || '默认值1' },
    { label: '字段2', value: basicInfo.field2 || '默认值2' }
  ]
}

// 在 tableColumns 计算属性中添加
if (props.config.type === 'newType') {
  return [
    { prop: 'column1', label: '列1', minWidth: 120 },
    { prop: 'column2', label: '列2', minWidth: 100 }
  ]
}
```

## 注意事项

1. 确保传递的数据结构与对应的显示类型要求一致
2. 表格数据中的 `id` 字段是必需的，用于行展开功能
3. 组件内部集成了所有逻辑，无需额外的子组件
4. 弹窗关闭时会自动重置展开状态
5. 支持的类型目前有 `rule` 和 `execute`，可根据需要扩展

## 优势

- **高度集成**：一个组件完成所有功能，无需管理多个子组件
- **配置简单**：只需要设置 `type` 即可自动适配所有显示逻辑
- **维护方便**：所有相关代码集中在一个文件中，便于维护和调试
- **性能优化**：避免了动态组件加载的开销
- **UI 统一**：使用 Element Plus 的 el-descriptions 组件，保持界面风格一致
- **响应式设计**：支持不同屏幕尺寸下的良好显示效果
