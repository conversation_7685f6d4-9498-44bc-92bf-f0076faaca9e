# 映射关系页面

## 功能概述

映射关系页面用于管理数据标准与元数据之间的映射关系，支持有效映射和无效映射的查看与管理。

## 页面结构

### 标签页
- **有效映射** - 显示当前生效的映射关系
- **无效映射** - 显示已失效的映射关系

### 搜索功能
- **选择标准集** - 下拉选择标准集进行过滤
- **关键词搜索** - 支持按标准中文名称/编码搜索
- **搜索/重置按钮** - 图标按钮，简洁美观

### 数据表格
显示映射关系的详细信息：
- 序号
- 标准中文名称
- 标准编码
- 所属标准集
- 映射元数据名称
- 映射元数据编码
- 所属资产
- 最近执行记录（包含执行时间和状态）
- 操作按钮

## 功能特性

### 有效映射操作
1. **置为无效映射** - 将有效映射标记为无效
2. **配置质量监控** - 为映射关系配置质量监控规则
3. **解除映射关系** - 提供两种解除方式：
   - 解除并加入无效映射
   - 仅解除，不记录无效映射

### 无效映射操作
1. **置为有效映射** - 将无效映射重新激活
2. **删除映射关系** - 永久删除映射关系

### 解除映射关系弹窗
提供两种解除方式的选择：

#### 解除并加入无效映射
- 被解除的映射关系从有效映射清单删除
- 同时加入无效映射关系清单
- 后续已归档的该映射关系不会生效

#### 仅解除，不记录无效映射
- 被解除的映射关系从有效映射清单删除
- 不记录到无效映射清单
- 再次映射需重新执行落标映射
- 后续已归档的该映射关系会重新添加

## 技术实现

### 响应式设计
- 使用 Vue 3 Composition API
- 响应式数据管理
- 组件化设计

### 数据管理
- 标签页切换自动刷新数据
- 搜索条件实时过滤
- 分页功能支持

### 用户交互
- 确认对话框防止误操作
- 操作反馈提示
- 加载状态管理

### 样式设计
- 现代化UI设计
- 响应式布局
- 统一的视觉风格
- 清晰的操作按钮分组

## 使用说明

1. **查看映射关系**
   - 切换标签页查看有效/无效映射
   - 使用搜索功能快速定位

2. **管理映射关系**
   - 根据需要置为有效/无效
   - 配置质量监控规则
   - 谨慎使用解除/删除功能

3. **解除映射关系**
   - 选择合适的解除方式
   - 理解两种方式的区别
   - 确认操作后生效

## 页面布局

```
┌─────────────────────────────────────────────────────────────┐
│ [有效映射] [无效映射]                                        │
├─────────────────────────────────────────────────────────────┤
│ 选择标准集: [下拉选择] 关键词: [输入框] [🔍] [🔄]           │
├─────────────────────────────────────────────────────────────┤
│ 序号 │ 标准中文名称 │ 标准编码 │ 所属标准集 │ ... │ 操作    │
│  1   │    标题     │  title   │  基础标准集 │ ... │ [按钮组] │
│  2   │    标题     │  yyds    │  基础标准集 │ ... │ [按钮组] │
├─────────────────────────────────────────────────────────────┤
│                    [分页控件]                                │
└─────────────────────────────────────────────────────────────┘
```

## 操作流程

### 解除映射关系流程
1. 点击"解除映射关系"按钮
2. 弹出选择对话框
3. 选择解除方式：
   - ○ 解除并加入无效映射
   - ○ 仅解除，不记录无效映射
4. 点击确定执行操作

### 数据状态转换
```
有效映射 ──[置为无效]──> 无效映射
    │                      │
    │                      │
    └──[解除关系]──> 删除   │
                           │
无效映射 ──[置为有效]──────┘
    │
    └──[删除]──> 永久删除
```

## 注意事项

- 删除操作不可恢复，请谨慎操作
- 解除映射关系会影响数据处理流程
- 建议在操作前备份重要的映射配置
- 质量监控配置需要单独实现
- 标签页切换会自动刷新数据
