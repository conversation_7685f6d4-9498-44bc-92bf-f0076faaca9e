# 调度设置功能使用说明

本文档说明了 RuleDrawer 组件中调度设置功能的实现和使用方法。

## 功能概述

调度设置功能允许用户为映射规则配置定时执行计划，支持多种执行频率和自定义 Cron 表达式。

## 功能特性

- **多种执行频率**：支持每天、每周、每月和自定义频率
- **可视化配置**：通过表单界面配置调度参数
- **Cron 表达式生成**：自动生成标准 Cron 表达式
- **时间范围控制**：支持设置执行的时间范围
- **实时预览**：实时显示生成的 Cron 表达式

## 实现结构

### 1. 触发按钮

```vue
<el-form-item v-if="form.executeMethod === 'scheduled'" label="执行周期" prop="executeCycle">
    <div class="schedule-config">
        <el-button type="primary" @click="openScheduleDialog">配置调度周期</el-button>
        <div v-if="form.executeCycle" class="schedule-display">
            <el-text type="success">已配置调度周期</el-text>
            <el-text type="info" style="margin-left: 8px;">{{ form.executeCycle }}</el-text>
        </div>
    </div>
</el-form-item>
```

### 2. 调度设置弹窗

```vue
<el-dialog
    v-model="scheduleDialogVisible"
    title="调度设置"
    width="60%"
    append-to-body
    draggable
    @close="cancelSchedule"
>
    <!-- 弹窗内容 -->
</el-dialog>
```

### 3. 数据结构

```javascript
const scheduleForm = reactive({
    dateRange: [],              // 执行时间范围
    frequency: 'daily',         // 执行频率
    dailyTime: '09:00:00',     // 每日执行时间
    weekDays: ['1'],           // 每周执行日期
    weeklyTime: '09:00:00',    // 每周执行时间
    cronExpression: '0 0 9 * * ?' // 自定义Cron表达式
})
```

## 配置选项

### 执行频率类型

#### 1. 每天执行 (daily)
- **配置项**：执行时间
- **生成规则**：`秒 分 时 * * ?`
- **示例**：每天 09:00:00 执行 → `0 0 9 * * ?`

#### 2. 每周执行 (weekly)
- **配置项**：执行日期（周一到周日）、执行时间
- **生成规则**：`秒 分 时 ? * 星期`
- **示例**：每周一、三、五 09:00:00 执行 → `0 0 9 ? * 1,3,5`

#### 3. 每月执行 (monthly)
- **配置项**：执行日期、执行时间
- **生成规则**：`秒 分 时 日 * ?`
- **示例**：每月1号 09:00:00 执行 → `0 0 9 1 * ?`

#### 4. 自定义 (custom)
- **配置项**：直接输入 Cron 表达式
- **验证**：支持标准 Cron 表达式格式
- **示例**：`0 30 10,14,16 * * ?` （每天10:30、14:30、16:30执行）

### Cron 表达式生成逻辑

```javascript
const generatedCron = computed(() => {
    const { frequency, dailyTime, weekDays, weeklyTime, cronExpression } = scheduleForm
    
    if (frequency === 'custom') {
        return cronExpression || '0 0 9 * * ?'
    }
    
    if (frequency === 'daily') {
        const [hour, minute, second] = (dailyTime || '09:00:00').split(':')
        return `${second} ${minute} ${hour} * * ?`
    }
    
    if (frequency === 'weekly') {
        const [hour, minute, second] = (weeklyTime || '09:00:00').split(':')
        const days = weekDays.length > 0 ? weekDays.join(',') : '1'
        return `${second} ${minute} ${hour} ? * ${days}`
    }
    
    return '0 0 9 * * ?'
})
```

## 用户交互流程

### 配置流程
1. 用户选择"定时执行"方式
2. 点击"配置调度周期"按钮
3. 在弹窗中设置执行时间范围
4. 选择执行频率类型
5. 根据频率类型配置具体参数
6. 查看生成的 Cron 表达式
7. 点击"保存"完成配置

### 修改流程
1. 已配置的规则显示当前 Cron 表达式
2. 点击"配置调度周期"按钮重新配置
3. 修改相关参数
4. 保存新的配置

## 界面布局

### 主界面显示
```
执行方式: [定时执行 ▼]
执行周期: [配置调度周期] 
         ✓ 已配置调度周期 0 0 9 * * ?
```

### 弹窗界面布局
```
┌─ 调度设置 ──────────────────────────────────────┐
│ ⚠️ 提示：请按照以下步骤进行操作...                │
│                                                │
│ 🕐 执行时间范围                                │
│    [开始日期] 至 [结束日期]                     │
│                                                │
│ ⚙️ 调度周期设置                                │
│    执行频率: [每天 ▼]                          │
│    执行时间: [09:00:00]                        │
│    生成的Cron: 0 0 9 * * ?                     │
│                                                │
│                           [取消] [保存]         │
└────────────────────────────────────────────────┘
```

## 样式设计

### 配置显示样式
```scss
.schedule-config {
    .schedule-display {
        margin-top: 12px;
        padding: 12px;
        background: #f0f9ff;
        border: 1px solid #b3d8ff;
        border-radius: 4px;
        
        .el-text {
            margin-right: 8px;
        }
    }
}
```

### 弹窗内容样式
```scss
.schedule-dialog-content {
    .schedule-section {
        margin-bottom: 24px;
        
        .section-title {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            font-size: 14px;
            font-weight: 600;
            color: #303133;
            
            .el-icon {
                margin-right: 8px;
                color: #409eff;
            }
        }
        
        .section-content {
            padding-left: 24px;
        }
    }
}
```

## 方法说明

### 核心方法

```javascript
// 打开调度设置弹窗
const openScheduleDialog = () => {
    scheduleDialogVisible.value = true
}

// 取消调度设置
const cancelSchedule = () => {
    scheduleDialogVisible.value = false
}

// 保存调度设置
const saveSchedule = () => {
    form.executeCycle = generatedCron.value
    scheduleDialogVisible.value = false
    ElMessage.success('调度周期配置成功')
}
```

## 扩展功能

### 1. 高级 Cron 配置
可以扩展支持更复杂的 Cron 表达式：

```javascript
// 支持年份配置
const generateAdvancedCron = () => {
    // 包含年份的7位Cron表达式
    return `${second} ${minute} ${hour} ${day} ${month} ${week} ${year}`
}
```

### 2. 预设模板
提供常用的调度模板：

```javascript
const scheduleTemplates = [
    { name: '工作日早上', cron: '0 0 9 ? * MON-FRI' },
    { name: '每周一早上', cron: '0 0 9 ? * MON' },
    { name: '每月第一天', cron: '0 0 9 1 * ?' }
]
```

### 3. 时区支持
支持不同时区的调度配置：

```javascript
const timezoneOptions = [
    { label: 'UTC+8 (北京时间)', value: 'Asia/Shanghai' },
    { label: 'UTC+0 (格林威治时间)', value: 'UTC' }
]
```

## 注意事项

1. **Cron 表达式格式**：使用标准的6位Cron表达式（秒 分 时 日 月 周）
2. **时间验证**：确保输入的时间格式正确
3. **周期冲突**：避免设置过于频繁的执行周期
4. **时区考虑**：注意服务器时区与用户时区的差异
5. **权限控制**：根据用户权限控制调度配置的可用性

## 集成示例

### 在父组件中使用

```vue
<template>
    <RuleDrawer
        v-model="drawerVisible"
        :title="drawerTitle"
        :data="drawerData"
        @save="handleSaveRule"
    />
</template>

<script setup>
const handleSaveRule = (formData) => {
    console.log('调度周期:', formData.executeCycle)
    // 处理保存逻辑
}
</script>
```

这个调度设置功能提供了完整的定时任务配置能力，支持多种执行频率和灵活的自定义配置，满足不同场景下的调度需求。
