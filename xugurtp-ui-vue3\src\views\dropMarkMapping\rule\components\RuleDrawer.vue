<template>
    <el-drawer v-model="visible" :title="title" direction="rtl" size="60%" class="rule-drawer">
        <div class="drawer-content">
            <el-form ref="ruleFormRef" :model="form" :rules="rules" label-width="140px" label-position="left">
                <!-- 基本信息 -->
                <div class="form-section">
                    <h3 class="section-title">基本信息</h3>
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="映射规则名称" prop="ruleName" required>
                                <el-input v-model="form.ruleName" placeholder="请输入规则名称" style="width: 400px"
                                    :disabled="isReadonly" />
                            </el-form-item>
                        </el-col>

                        <el-col :span="12">
                            <el-form-item label="描述" prop="description">
                                <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入规则描述"
                                    style="width: 400px" :disabled="isReadonly" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>

                <!-- 圈选数据标准 -->
                <div class="form-section">
                    <h3 class="section-title">圈选数据标准</h3>
                    <p class="section-desc">圈选指定标准集/标准，圈选的资产对象基于配置进行映射匹配</p>

                    <el-form-item label="来源标准集/标准" prop="sourceStandard" required>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <div class="catalog-tree-container">
                                    <el-tree-v2
                                        :data="catalogTreeData"
                                        :props="{ children: 'children', label: 'name' }"
                                        node-key="id"
                                        show-checkbox
                                        :height="200"
                                        :default-expanded-keys="defaultExpandedKeys"
                                        @check="handleCatalogCheck"
                                        :disabled="isReadonly"
                                    />
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <el-select
                                    v-model="form.selectedStandards"
                                    placeholder="请选择标准"
                                    style="width: 100%"
                                    multiple
                                    filterable
                                    :disabled="isReadonly"
                                    @change="handleStandardChange"
                                >
                                    <el-option
                                        v-for="item in standardListData"
                                        :key="item.id"
                                        :label="item.name"
                                        :value="item.id"
                                    />
                                </el-select>
                            </el-col>
                        </el-row>

                    </el-form-item>

                </div>

                <!-- 圈选资产对象 -->

                <div class="form-section">
                    <h3 class="section-title">圈选资产对象</h3>
                    <el-form-item label="映射数据对象" prop="mappingDataObject" required>
                        <el-select v-model="form.mappingDataObject" placeholder="可以多选择数据库/数据表/元数据，可多选"
                            style="width: 400px" multiple filterable :disabled="isReadonly">
                            <el-option v-for="item in dataObjectOptions" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>

                </div>

                <!-- 映射配置 -->
                <div class="form-section">
                    <h3 class="section-title">映射配置</h3>
                    <el-text type="info" size="small">无法删除，至少配置1条规则</el-text>

                    <!-- 相似度匹配配置 -->
                    <el-form-item v-if="form.mappingMethod === 'similarity'" label="相似度阈值">
                        <el-input-number v-model="form.similarityThreshold" :min="0" :max="100" style="width: 120px"
                            :disabled="isReadonly" />
                        <span style="margin-left: 8px">%</span>
                    </el-form-item>

                    <!-- 映射规则配置 -->
                    <div class="mapping-rules">
                        <div class="rule-header">
                        </div>

                        <div class="rules-container">
                            <div v-for="(rule, index) in form.mappingRules" :key="index" class="mapping-rule-item">
                                <div class="rule-row">
                                    <el-select v-model="rule.leftField" placeholder="字段名称（单选）" style="width: 150px"
                                        :disabled="isReadonly">
                                        <el-option label="字段名称" value="fieldName" />
                                        <el-option label="字段名称（中文）" value="fieldNameCn" />
                                        <el-option label="字段名称（英文）" value="fieldNameEn" />
                                        <el-option label="字段编码" value="fieldCode" />
                                    </el-select>

                                    <el-select v-model="rule.leftType" style="width: 100px" :disabled="isReadonly">
                                        <el-option label="STRING" value="STRING" />
                                    </el-select>

                                    <span class="operator">=</span>

                                    <el-select v-model="rule.rightField" placeholder="标准中文名称（单选）" style="width: 150px"
                                        :disabled="isReadonly">
                                        <el-option label="标准中文名称" value="standardNameCn" />
                                    </el-select>

                                    <el-select v-model="rule.rightType" style="width: 100px" :disabled="isReadonly">
                                        <el-option label="STRING" value="STRING" />
                                    </el-select>

                                    <el-button v-if="form.mappingRules.length > 1 && !isReadonly" type="danger"
                                        icon="Delete" circle size="small" @click="removeRule(index)" />
                                </div>

                                <!-- 连接线和连接符 -->
                                <div v-if="index < form.mappingRules.length - 1" class="rule-connector">
                                    <div class="connector-line">
                                        <div class="line-top"></div>
                                        <div class="line-horizontal"></div>
                                        <div class="line-bottom"></div>
                                    </div>
                                    <div class="connector-select">
                                        <el-select v-model="rule.connector" style="width: 80px" size="small"
                                            :disabled="isReadonly">
                                            <el-option label="且" value="AND" />
                                            <el-option label="或" value="OR" />
                                        </el-select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div v-if="!isReadonly" class="add-rule-btn">
                            <el-button type="primary" icon="Plus" @click="addRule">
                                添加规则
                            </el-button>
                        </div>
                    </div>
                </div>

                <!-- 执行配置 -->
                <div class="form-section">
                    <h3 class="section-title">执行配置</h3>

                    <el-form-item label="执行方式" prop="executeMethod" required>
                        <el-radio-group v-model="form.executeMethod" :disabled="isReadonly">
                            <el-radio label="scheduled">定时执行</el-radio>
                            <el-radio label="manual">手动执行</el-radio>
                        </el-radio-group>
                    </el-form-item>

                    <!-- 定时执行配置 -->
                    <div v-if="form.executeMethod === 'scheduled'" class="schedule-config">
                        <el-alert title="提示" type="warning" description="请按照以下步骤进行操作：
                            1. 选择适当的时间。
                            2. 生成相应的 Cron 表达式。
                            3. 确保保存所做的更改。
                            4. 注意：务必不要忽略选择秒时段。" :closable="false" style="margin-bottom: 20px;">
                        </el-alert>

                        <el-row>
                            <el-col :span="24" style="margin: 20px 0 20px 0">
                                <el-date-picker v-model="value1" type="datetimerange" range-separator="至"
                                    start-placeholder="开始日期" end-placeholder="结束日期" value-format="YYYY-MM-DD HH:mm:ss"
                                    :disabled="isReadonly">
                                </el-date-picker>
                            </el-col>

                            <el-col :span="24">
                                <vue3Cron v-if="showCron" :project-code-of-ds="projectCodeOfDs"
                                    :workspace-id="workspaceId" :datetimerange="datetimerange" :CronData="crontab"
                                    @change="handleCronChange" />
                            </el-col>
                        </el-row>
                    </div>
                </div>
            </el-form>
        </div>

        <template #footer>
            <div class="drawer-footer">
                <el-button @click="handleCancel">取消</el-button>
                <el-button type="primary" @click="handleSave">确定</el-button>
            </div>
        </template>
    </el-drawer>


</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import vue3Cron from '@/components/vue3Cron'
import { getCatalogTree, getDataStandardList } from '@/api/datamodel'
// Props
const props = defineProps({
    modelValue: {
        type: Boolean,
        default: false
    },
    title: {
        type: String,
        default: '新建映射规则'
    },
    data: {
        type: Object,
        default: () => ({})
    }
})

// Emits
const emit = defineEmits(['update:modelValue', 'save'])

// 响应式数据
const visible = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val)
})

// 是否为只读模式
const isReadonly = computed(() => props.data?.readonly === true)

const ruleFormRef = ref()

// 调度配置相关数据
const value1 = ref([])
const showCron = ref(true)
const projectCodeOfDs = ref('')
const workspaceId = ref('1') // 默认工作空间ID
const datetimerange = ref([])
const crontab = ref('')

// 标准目录和标准列表数据
const catalogTreeData = ref([])
const standardListData = ref([])
const defaultExpandedKeys = ref([])
const selectedCatalogIds = ref([])

const form = reactive({
    ruleName: '',
    description: '',
    sourceStandard: '',
    selectedStandards: [],
    mappingDataObject: [],
    mappingMethod: 'attribute',
    similarityThreshold: 80,
    mappingRules: [
        {
            leftField: 'fieldName',
            leftType: 'STRING',
            rightField: 'standardNameCn',
            rightType: 'STRING',
            connector: 'AND'
        },
        {
            leftField: 'fieldNameCn',
            leftType: 'STRING',
            rightField: 'standardNameCn',
            rightType: 'STRING',
            connector: 'OR'
        },
        {
            leftField: 'fieldCode',
            leftType: 'STRING',
            rightField: 'standardNameCn',
            rightType: 'STRING',
            connector: 'AND'
        }
    ],
    executeMethod: 'manual',
    executeCycle: ''
})

const rules = {
    ruleName: [
        { required: true, message: '请输入映射规则名称', trigger: 'blur' }
    ],
    sourceStandard: [
        { required: true, message: '请选择来源标准集/标准', trigger: 'change' }
    ],
    mappingDataObject: [
        { required: true, message: '请选择映射数据对象', trigger: 'change' }
    ],
    mappingMethod: [
        { required: true, message: '请选择映射方式', trigger: 'change' }
    ],
    executeMethod: [
        { required: true, message: '请选择执行方式', trigger: 'change' }
    ]
}



const dataObjectOptions = [
    { label: '数据库1/表1/字段1', value: 'db1_table1_field1' },
    { label: '数据库1/表1/字段2', value: 'db1_table1_field2' },
    { label: '数据库2/表2/字段1', value: 'db2_table2_field1' }
]



// 方法
const resetForm = () => {
    Object.assign(form, {
        ruleName: '',
        description: '',
        sourceStandard: '',
        selectedStandards: [],
        mappingDataObject: [],
        mappingMethod: 'attribute',
        similarityThreshold: 80,
        mappingRules: [
            {
                leftField: '',
                leftType: 'STRING',
                rightField: '',
                rightType: 'STRING',
                connector: 'AND'
            }
        ],
        executeMethod: 'manual',
        executeCycle: ''
    })
}

const addRule = () => {
    form.mappingRules.push({
        leftField: '',
        leftType: 'STRING',
        rightField: '',
        rightType: 'STRING',
        connector: 'AND'
    })
}

const removeRule = (index) => {
    if (form.mappingRules.length > 1) {
        form.mappingRules.splice(index, 1)
    }
}

const handleCancel = () => {
    visible.value = false
}

const handleSave = () => {
    ruleFormRef.value.validate((valid) => {
        if (valid) {
            emit('save', { ...form })
            visible.value = false
        } else {
            ElMessage.error('请完善表单信息')
        }
    })
}

// 处理Cron表达式变化
const handleCronChange = (cronData) => {
    crontab.value = cronData
    // 可以将cron数据保存到form中
    form.executeCycle = cronData
}

// 获取目录树数据
const getCatalogTreeUtil = async () => {
    try {
        const query = {
            workspaceId: workspaceId.value,
            type: '2',
            searchName: '',
        }
        const res = await getCatalogTree(query)
        if (res.code === 200) {
            catalogTreeData.value = res.data || []
            // 设置默认展开的节点
            if (catalogTreeData.value.length > 0) {
                defaultExpandedKeys.value = [catalogTreeData.value[0].id]
            }
        }
    } catch (error) {
        console.error('获取目录树失败:', error)
        ElMessage.error('获取目录树失败')
    }
}

// 获取标准列表数据
const getListCatalogUtil = async (catalogId) => {
    try {
        catalogId = Number(catalogId) ? catalogId : ''
        const query = {
            catalogId: catalogId,
            pageNum: 1,
            pageSize: 100,
            workspaceId: workspaceId.value,
            type: 2,
        }
        const res = await getDataStandardList(query)
        if (res.code === 200) {
            standardListData.value = res.rows?.map((item) => ({
                id: item.id,
                name: item.name,
                version: item.version,
                versionLabel: 'V' + item.version
            })) || []
        }
    } catch (error) {
        console.error('获取标准列表失败:', error)
        ElMessage.error('获取标准列表失败')
    }
}

// 处理目录树选择
const handleCatalogCheck = (checkedKeys) => {
    selectedCatalogIds.value = checkedKeys
    // 当选择目录时，获取对应的标准列表
    if (checkedKeys.length > 0) {
        getListCatalogUtil(checkedKeys[0])
    } else {
        standardListData.value = []
    }
}

// 处理标准选择
const handleStandardChange = (selectedValues) => {
    form.selectedStandards = selectedValues
}



// 监听数据变化
watch(() => props.data, (newData) => {
    if (newData && Object.keys(newData).length > 0) {
        Object.assign(form, {
            ruleName: newData.ruleName || '',
            description: newData.description || '',
            // 其他字段根据实际需要填充
        })
    }
}, { immediate: true })

// 监听visible变化，关闭时重置表单
watch(visible, (newVal) => {
    if (!newVal) {
        nextTick(() => {
            resetForm()
            ruleFormRef.value?.clearValidate()
        })
    } else {
        // 打开时获取目录树数据
        getCatalogTreeUtil()
    }
})
</script>

<style lang="scss" scoped>
.rule-drawer {
    :deep(.el-drawer__body) {
        padding: 0;
    }

    .drawer-content {
        padding: 20px;
        height: calc(100% - 60px);
        overflow-y: auto;

        .form-section {
            margin-bottom: 32px;

            .section-title {
                margin: 0 0 16px 0;
                font-size: 16px;
                font-weight: 600;
                color: #303133;
                padding-left: 12px;
                position: relative;

                &::before {
                    content: '';
                    position: absolute;
                    left: 0;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 4px;
                    height: 16px;
                    background-color: #409eff;
                    border-radius: 2px;
                }
            }

            .section-desc {
                margin: 0 0 16px 0;
                color: #606266;
                font-size: 14px;
            }

            .search-hint {
                margin-top: 8px;
                margin-bottom: 16px;
            }

            .standard-tree {
                border: 1px solid #dcdfe6;
                border-radius: 4px;
                padding: 12px;
                max-height: 200px;
                overflow-y: auto;
            }

            .catalog-tree-container {
                border: 1px solid #dcdfe6;
                border-radius: 4px;
                // padding: 8px;
                background: #fff;
                width: 100%;
            }

            .mapping-rules {
                .rule-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 16px;

                    span:first-child {
                        font-weight: 600;
                        color: #303133;
                    }
                }

                .rules-container {
                    max-height: 300px;
                    overflow-y: auto;
                    border: 1px solid #e4e7ed;
                    border-radius: 6px;
                    padding: 16px;
                    margin-bottom: 16px;
                    background: #fafafa;

                    &::-webkit-scrollbar {
                        width: 6px;
                    }

                    &::-webkit-scrollbar-track {
                        background: #f1f1f1;
                        border-radius: 3px;
                    }

                    &::-webkit-scrollbar-thumb {
                        background: #c1c1c1;
                        border-radius: 3px;

                        &:hover {
                            background: #a8a8a8;
                        }
                    }
                }

                .mapping-rule-item {
                    position: relative;
                    margin-bottom: 0;

                    .rule-row {
                        display: flex;
                        align-items: center;
                        gap: 12px;
                        padding: 12px;
                        background: #fff;
                        border: 1px solid #e4e7ed;
                        border-radius: 6px;
                        margin-bottom: 8px;

                        .operator {
                            font-weight: 600;
                            color: #303133;
                            padding: 0 8px;
                            font-size: 16px;
                        }
                    }

                    .rule-connector {
                        position: relative;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        height: 40px;
                        margin-bottom: 8px;

                        .connector-line {
                            position: absolute;
                            left: 50%;
                            transform: translateX(-50%);
                            width: 2px;
                            height: 100%;
                            display: flex;
                            flex-direction: column;

                            .line-top {
                                flex: 1;
                                border-left: 2px dashed #d3d3d3;
                            }

                            .line-horizontal {
                                width: 60px;
                                height: 2px;
                                border-top: 2px dashed #d3d3d3;
                                margin-left: -29px;
                                position: relative;
                                z-index: 1;
                            }

                            .line-bottom {
                                flex: 1;
                                border-left: 2px dashed #d3d3d3;
                            }
                        }

                        .connector-select {
                            position: relative;
                            z-index: 2;
                            background: #fafafa;
                            padding: 0 8px;
                        }
                    }
                }

                .add-rule-btn {
                    text-align: center;
                    padding-top: 8px;
                }
            }

            // 调度配置样式
            .schedule-config {
                margin-top: 20px;
                padding: 20px;
                background: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 8px;

                .el-alert {
                    margin-bottom: 20px;
                }

                .el-date-picker {
                    width: 100%;
                }
            }
        }
    }



    .drawer-footer {
        display: flex;
        justify-content: flex-end;
        gap: 12px;
        padding: 16px 20px;
        border-top: 1px solid #e4e7ed;
        background: #fff;
    }
}
</style>
