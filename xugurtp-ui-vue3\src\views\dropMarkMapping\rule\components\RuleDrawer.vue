<template>
    <el-drawer v-model="visible" :title="title" direction="rtl" size="60%" class="rule-drawer">
        <div class="drawer-content">
            <el-form ref="ruleFormRef" :model="form" :rules="rules" label-width="140px" label-position="left">
                <!-- 基本信息 -->
                <div class="form-section">
                    <h3 class="section-title">基本信息</h3>
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="映射规则名称" prop="ruleName" required>
                                <el-input v-model="form.ruleName" placeholder="请输入规则名称" style="width: 400px"
                                    :disabled="isReadonly" />
                            </el-form-item>
                        </el-col>

                        <el-col :span="12">
                            <el-form-item label="描述" prop="description">
                                <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入规则描述"
                                    style="width: 400px" :disabled="isReadonly" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>

                <!-- 圈选数据标准 -->
                <div class="form-section">
                    <!-- <h3 class="section-title">圈选数据标准</h3>
                    <p class="section-desc">圈选指定标准集/标准，圈选的资产对象基于配置进行映射匹配</p> -->
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="来源标准集/标准" prop="sourceStandard" required>
                                <el-row :gutter="20">
                                    <el-col :span="12">
                                        <el-select v-model="form.sourceStandard" placeholder="请选择标准目录"
                                            style="width: 90%" filterable :disabled="isReadonly">
                                            <el-option v-for="item in standardOptions" :key="item.value"
                                                :label="item.label" :value="item.value" />
                                        </el-select>
                                    </el-col>
                                    <el-col :span="12">

                                        <el-tree :data="standardTreeData" show-checkbox node-key="id"
                                            :default-expanded-keys="[1]" :default-checked-keys="[]"
                                            :props="{ children: 'children', label: 'label' }" style="width: 50%"
                                            :disabled="isReadonly" />
                                    </el-col>
                                </el-row>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="映射数据对象" prop="mappingDataObject" required>
                                <el-select v-model="form.mappingDataObject" placeholder="可以多选择数据库/数据表/元数据，可多选"
                                    style="width: 400px" multiple filterable :disabled="isReadonly">
                                    <el-option v-for="item in dataObjectOptions" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>

                <!-- 圈选资产对象 -->
                <div class="form-section">
                    <h3 class="section-title">映射配置</h3>
                    <el-text type="info" size="small">无法删除，至少配置1条规则</el-text>


                </div>

                <!-- 映射配置 -->
                <div class="form-section">


                    <!-- 相似度匹配配置 -->
                    <el-form-item v-if="form.mappingMethod === 'similarity'" label="相似度阈值">
                        <el-input-number v-model="form.similarityThreshold" :min="0" :max="100" style="width: 120px"
                            :disabled="isReadonly" />
                        <span style="margin-left: 8px">%</span>
                    </el-form-item>

                    <!-- 映射规则配置 -->
                    <div class="mapping-rules">
                        <div class="rule-header">
                        </div>

                        <div class="rules-container">
                            <div v-for="(rule, index) in form.mappingRules" :key="index" class="mapping-rule-item">
                                <div class="rule-row">
                                    <el-select v-model="rule.leftField" placeholder="字段名称（单选）" style="width: 150px"
                                        :disabled="isReadonly">
                                        <el-option label="字段名称" value="fieldName" />
                                        <el-option label="字段名称（中文）" value="fieldNameCn" />
                                        <el-option label="字段名称（英文）" value="fieldNameEn" />
                                        <el-option label="字段编码" value="fieldCode" />
                                    </el-select>

                                    <el-select v-model="rule.leftType" style="width: 100px" :disabled="isReadonly">
                                        <el-option label="STRING" value="STRING" />
                                    </el-select>

                                    <span class="operator">=</span>

                                    <el-select v-model="rule.rightField" placeholder="标准中文名称（单选）" style="width: 150px"
                                        :disabled="isReadonly">
                                        <el-option label="标准中文名称" value="standardNameCn" />
                                    </el-select>

                                    <el-select v-model="rule.rightType" style="width: 100px" :disabled="isReadonly">
                                        <el-option label="STRING" value="STRING" />
                                    </el-select>

                                    <el-button v-if="form.mappingRules.length > 1 && !isReadonly" type="danger"
                                        icon="Delete" circle size="small" @click="removeRule(index)" />
                                </div>

                                <!-- 连接线和连接符 -->
                                <div v-if="index < form.mappingRules.length - 1" class="rule-connector">
                                    <div class="connector-line">
                                        <div class="line-top"></div>
                                        <div class="line-horizontal"></div>
                                        <div class="line-bottom"></div>
                                    </div>
                                    <div class="connector-select">
                                        <el-select v-model="rule.connector" style="width: 80px" size="small"
                                            :disabled="isReadonly">
                                            <el-option label="且" value="AND" />
                                            <el-option label="或" value="OR" />
                                        </el-select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div v-if="!isReadonly" class="add-rule-btn">
                            <el-button type="primary" icon="Plus" @click="addRule">
                                添加规则
                            </el-button>
                        </div>
                    </div>
                </div>

                <!-- 执行配置 -->
                <div class="form-section">
                    <h3 class="section-title">执行配置</h3>

                    <el-form-item label="执行方式" prop="executeMethod" required>
                        <el-radio-group v-model="form.executeMethod" :disabled="isReadonly">
                            <el-radio label="scheduled">定时执行</el-radio>
                            <el-radio label="manual">手动执行</el-radio>
                        </el-radio-group>
                    </el-form-item>

                    <el-form-item v-if="form.executeMethod === 'scheduled'" label="执行周期" prop="executeCycle">
                        <div class="schedule-config">
                            <el-button type="primary" @click="openScheduleDialog">配置调度周期</el-button>
                            <div v-if="form.executeCycle" class="schedule-display">
                                <el-text type="success">已配置调度周期</el-text>
                                <el-text type="info" style="margin-left: 8px;">{{ form.executeCycle }}</el-text>
                            </div>
                        </div>
                    </el-form-item>
                </div>
            </el-form>
        </div>

        <template #footer>
            <div class="drawer-footer">
                <el-button @click="handleCancel">取消</el-button>
                <el-button type="primary" @click="handleSave">确定</el-button>
            </div>
        </template>
    </el-drawer>

    <!-- 调度设置弹窗 -->
    <el-dialog v-model="scheduleDialogVisible" title="调度设置" width="60%" append-to-body draggable
        @close="cancelSchedule">
        <div class="schedule-dialog-content">
            <el-alert title="提示" type="info" description="请按照以下步骤进行操作：选择适当的时间、生成相应的 Cron 表达式、确保保存所做的更改。注意：务必不要忽略选择秒时段。"
                :closable="false" style="margin-bottom: 20px;" />

            <!-- 时间范围选择 -->
            <div class="schedule-section">
                <div class="section-title">
                    <el-icon>
                        <Clock />
                    </el-icon>
                    <span>执行时间范围</span>
                </div>
                <div class="section-content">
                    <el-date-picker v-model="scheduleForm.dateRange" type="datetimerange" range-separator="至"
                        start-placeholder="开始日期" end-placeholder="结束日期" value-format="YYYY-MM-DD HH:mm:ss"
                        style="width: 100%;" />
                </div>
            </div>

            <!-- Cron 表达式设置 -->
            <div class="schedule-section">
                <div class="section-title">
                    <el-icon>
                        <Setting />
                    </el-icon>
                    <span>调度周期设置</span>
                </div>
                <div class="section-content">
                    <div class="cron-config">
                        <el-form :model="scheduleForm" label-width="100px">
                            <el-form-item label="执行频率">
                                <el-select v-model="scheduleForm.frequency" placeholder="请选择执行频率" style="width: 200px;">
                                    <el-option label="每天" value="daily" />
                                    <el-option label="每周" value="weekly" />
                                    <el-option label="每月" value="monthly" />
                                    <el-option label="自定义" value="custom" />
                                </el-select>
                            </el-form-item>

                            <el-form-item v-if="scheduleForm.frequency === 'daily'" label="执行时间">
                                <el-time-picker v-model="scheduleForm.dailyTime" placeholder="选择时间" format="HH:mm:ss"
                                    value-format="HH:mm:ss" />
                            </el-form-item>

                            <el-form-item v-if="scheduleForm.frequency === 'weekly'" label="执行日期">
                                <el-checkbox-group v-model="scheduleForm.weekDays">
                                    <el-checkbox label="1">周一</el-checkbox>
                                    <el-checkbox label="2">周二</el-checkbox>
                                    <el-checkbox label="3">周三</el-checkbox>
                                    <el-checkbox label="4">周四</el-checkbox>
                                    <el-checkbox label="5">周五</el-checkbox>
                                    <el-checkbox label="6">周六</el-checkbox>
                                    <el-checkbox label="0">周日</el-checkbox>
                                </el-checkbox-group>
                            </el-form-item>

                            <el-form-item v-if="scheduleForm.frequency === 'weekly'" label="执行时间">
                                <el-time-picker v-model="scheduleForm.weeklyTime" placeholder="选择时间" format="HH:mm:ss"
                                    value-format="HH:mm:ss" />
                            </el-form-item>

                            <el-form-item v-if="scheduleForm.frequency === 'custom'" label="Cron表达式">
                                <el-input v-model="scheduleForm.cronExpression" placeholder="请输入Cron表达式，如：0 0 12 * * ?"
                                    style="width: 300px;" />
                            </el-form-item>

                            <el-form-item label="生成的Cron">
                                <el-text type="primary">{{ generatedCron }}</el-text>
                            </el-form-item>
                        </el-form>
                    </div>
                </div>
            </div>
        </div>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="cancelSchedule">取 消</el-button>
                <el-button type="primary" @click="saveSchedule">保 存</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Clock, Setting } from '@element-plus/icons-vue'
// Props
const props = defineProps({
    modelValue: {
        type: Boolean,
        default: false
    },
    title: {
        type: String,
        default: '新建映射规则'
    },
    data: {
        type: Object,
        default: () => ({})
    }
})

// Emits
const emit = defineEmits(['update:modelValue', 'save'])

// 响应式数据
const visible = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val)
})

// 是否为只读模式
const isReadonly = computed(() => props.data?.readonly === true)

const ruleFormRef = ref()

// 调度设置弹窗相关
const scheduleDialogVisible = ref(false)
const scheduleForm = reactive({
    dateRange: [],
    frequency: 'daily',
    dailyTime: '09:00:00',
    weekDays: ['1'],
    weeklyTime: '09:00:00',
    cronExpression: '0 0 9 * * ?'
})

const form = reactive({
    ruleName: '',
    description: '',
    sourceStandard: '',
    mappingDataObject: [],
    mappingMethod: 'attribute',
    similarityThreshold: 80,
    mappingRules: [
        {
            leftField: 'fieldName',
            leftType: 'STRING',
            rightField: 'standardNameCn',
            rightType: 'STRING',
            connector: 'AND'
        },
        {
            leftField: 'fieldNameCn',
            leftType: 'STRING',
            rightField: 'standardNameCn',
            rightType: 'STRING',
            connector: 'OR'
        },
        {
            leftField: 'fieldCode',
            leftType: 'STRING',
            rightField: 'standardNameCn',
            rightType: 'STRING',
            connector: 'AND'
        }
    ],
    executeMethod: 'manual',
    executeCycle: ''
})

const rules = {
    ruleName: [
        { required: true, message: '请输入映射规则名称', trigger: 'blur' }
    ],
    sourceStandard: [
        { required: true, message: '请选择来源标准集/标准', trigger: 'change' }
    ],
    mappingDataObject: [
        { required: true, message: '请选择映射数据对象', trigger: 'change' }
    ],
    mappingMethod: [
        { required: true, message: '请选择映射方式', trigger: 'change' }
    ],
    executeMethod: [
        { required: true, message: '请选择执行方式', trigger: 'change' }
    ]
}

const standardOptions = [
    { label: '标准集测试目录（1）', value: 'test_dir_1' },
    { label: '测试标准集', value: 'test_standard_set' }
]

const standardTreeData = [
    {
        id: 1,
        label: '标准集测试目录（1）',
        children: [
            {
                id: 2,
                label: '测试标准集'
            }
        ]
    }
]

const dataObjectOptions = [
    { label: '数据库1/表1/字段1', value: 'db1_table1_field1' },
    { label: '数据库1/表1/字段2', value: 'db1_table1_field2' },
    { label: '数据库2/表2/字段1', value: 'db2_table2_field1' }
]

// 生成Cron表达式的计算属性
const generatedCron = computed(() => {
    const { frequency, dailyTime, weekDays, weeklyTime, cronExpression } = scheduleForm

    if (frequency === 'custom') {
        return cronExpression || '0 0 9 * * ?'
    }

    if (frequency === 'daily') {
        const [hour, minute, second] = (dailyTime || '09:00:00').split(':')
        return `${second} ${minute} ${hour} * * ?`
    }

    if (frequency === 'weekly') {
        const [hour, minute, second] = (weeklyTime || '09:00:00').split(':')
        const days = weekDays.length > 0 ? weekDays.join(',') : '1'
        return `${second} ${minute} ${hour} ? * ${days}`
    }

    return '0 0 9 * * ?'
})

// 方法
const resetForm = () => {
    Object.assign(form, {
        ruleName: '',
        description: '',
        sourceStandard: '',
        mappingDataObject: [],
        mappingMethod: 'attribute',
        similarityThreshold: 80,
        mappingRules: [
            {
                leftField: '',
                leftType: 'STRING',
                rightField: '',
                rightType: 'STRING',
                connector: 'AND'
            }
        ],
        executeMethod: 'manual',
        executeCycle: ''
    })
}

const addRule = () => {
    form.mappingRules.push({
        leftField: '',
        leftType: 'STRING',
        rightField: '',
        rightType: 'STRING',
        connector: 'AND'
    })
}

const removeRule = (index) => {
    if (form.mappingRules.length > 1) {
        form.mappingRules.splice(index, 1)
    }
}

const handleCancel = () => {
    visible.value = false
}

const handleSave = () => {
    ruleFormRef.value.validate((valid) => {
        if (valid) {
            emit('save', { ...form })
            visible.value = false
        } else {
            ElMessage.error('请完善表单信息')
        }
    })
}

// 调度设置相关方法
const openScheduleDialog = () => {
    scheduleDialogVisible.value = true
}

const cancelSchedule = () => {
    scheduleDialogVisible.value = false
}

const saveSchedule = () => {
    // 保存调度设置
    form.executeCycle = generatedCron.value
    scheduleDialogVisible.value = false
    ElMessage.success('调度周期配置成功')
}

// 监听数据变化
watch(() => props.data, (newData) => {
    if (newData && Object.keys(newData).length > 0) {
        Object.assign(form, {
            ruleName: newData.ruleName || '',
            description: newData.description || '',
            // 其他字段根据实际需要填充
        })
    }
}, { immediate: true })

// 监听visible变化，关闭时重置表单
watch(visible, (newVal) => {
    if (!newVal) {
        nextTick(() => {
            resetForm()
            ruleFormRef.value?.clearValidate()
        })
    }
})
</script>

<style lang="scss" scoped>
.rule-drawer {
    :deep(.el-drawer__body) {
        padding: 0;
    }

    .drawer-content {
        padding: 20px;
        height: calc(100% - 60px);
        overflow-y: auto;

        .form-section {
            margin-bottom: 32px;

            .section-title {
                margin: 0 0 16px 0;
                font-size: 16px;
                font-weight: 600;
                color: #303133;
                border-bottom: 1px solid #e4e7ed;
                padding-bottom: 8px;
            }

            .section-desc {
                margin: 0 0 16px 0;
                color: #606266;
                font-size: 14px;
            }

            .search-hint {
                margin-top: 8px;
                margin-bottom: 16px;
            }

            .standard-tree {
                border: 1px solid #dcdfe6;
                border-radius: 4px;
                padding: 12px;
                max-height: 200px;
                overflow-y: auto;
            }

            .mapping-rules {
                .rule-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 16px;

                    span:first-child {
                        font-weight: 600;
                        color: #303133;
                    }
                }

                .rules-container {
                    max-height: 300px;
                    overflow-y: auto;
                    border: 1px solid #e4e7ed;
                    border-radius: 6px;
                    padding: 16px;
                    margin-bottom: 16px;
                    background: #fafafa;

                    &::-webkit-scrollbar {
                        width: 6px;
                    }

                    &::-webkit-scrollbar-track {
                        background: #f1f1f1;
                        border-radius: 3px;
                    }

                    &::-webkit-scrollbar-thumb {
                        background: #c1c1c1;
                        border-radius: 3px;

                        &:hover {
                            background: #a8a8a8;
                        }
                    }
                }

                .mapping-rule-item {
                    position: relative;
                    margin-bottom: 0;

                    .rule-row {
                        display: flex;
                        align-items: center;
                        gap: 12px;
                        padding: 12px;
                        background: #fff;
                        border: 1px solid #e4e7ed;
                        border-radius: 6px;
                        margin-bottom: 8px;

                        .operator {
                            font-weight: 600;
                            color: #303133;
                            padding: 0 8px;
                            font-size: 16px;
                        }
                    }

                    .rule-connector {
                        position: relative;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        height: 40px;
                        margin-bottom: 8px;

                        .connector-line {
                            position: absolute;
                            left: 50%;
                            transform: translateX(-50%);
                            width: 2px;
                            height: 100%;
                            display: flex;
                            flex-direction: column;

                            .line-top {
                                flex: 1;
                                border-left: 2px dashed #d3d3d3;
                            }

                            .line-horizontal {
                                width: 60px;
                                height: 2px;
                                border-top: 2px dashed #d3d3d3;
                                margin-left: -29px;
                                position: relative;
                                z-index: 1;
                            }

                            .line-bottom {
                                flex: 1;
                                border-left: 2px dashed #d3d3d3;
                            }
                        }

                        .connector-select {
                            position: relative;
                            z-index: 2;
                            background: #fafafa;
                            padding: 0 8px;
                        }
                    }
                }

                .add-rule-btn {
                    text-align: center;
                    padding-top: 8px;
                }
            }

            .schedule-config {
                .schedule-display {
                    margin-top: 12px;
                    padding: 12px;
                    background: #f0f9ff;
                    border: 1px solid #b3d8ff;
                    border-radius: 4px;

                    .el-text {
                        margin-right: 8px;
                    }
                }
            }
        }
    }

    // 调度设置弹窗样式
    .schedule-dialog-content {
        .schedule-section {
            margin-bottom: 24px;

            .section-title {
                display: flex;
                align-items: center;
                margin-bottom: 16px;
                font-size: 14px;
                font-weight: 600;
                color: #303133;

                .el-icon {
                    margin-right: 8px;
                    color: #409eff;
                }
            }

            .section-content {
                padding-left: 24px;

                .cron-config {
                    .el-form-item {
                        margin-bottom: 16px;
                    }

                    .el-checkbox-group {
                        .el-checkbox {
                            margin-right: 16px;
                            margin-bottom: 8px;
                        }
                    }
                }
            }
        }
    }

    .drawer-footer {
        display: flex;
        justify-content: flex-end;
        gap: 12px;
        padding: 16px 20px;
        border-top: 1px solid #e4e7ed;
        background: #fff;
    }
}
</style>
