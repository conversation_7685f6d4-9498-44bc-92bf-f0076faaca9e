# 状态开关功能使用说明

本文档说明了如何在表格中使用状态开关组件，包括确认对话框和回退机制。

## 功能特性

- **直观的开关界面**：使用 Element Plus 的 el-switch 组件
- **确认对话框**：防止误操作，用户可以确认或取消状态变更
- **自动回退**：用户取消时，开关自动回退到原始状态
- **加载状态**：状态更新过程中禁用开关，防止重复操作
- **错误处理**：API 调用失败时的错误提示和数据恢复

## 实现方式

### 1. 模板部分

```vue
<el-table-column prop="status" label="生效状态" min-width="120" align="center">
  <template #default="scope">
    <el-switch
      :model-value="scope.row.status === '生效'"
      :disabled="updatingStatusIds.has(scope.row.id)"
      active-text="生效"
      inactive-text="失效"
      active-color="#67c23a"
      inactive-color="#f56c6c"
      inline-prompt
      @change="(value) => handleStatusChange(scope.row, value)"
    />
  </template>
</el-table-column>
```

### 2. 数据定义

```javascript
// 状态更新中的规则ID集合
const updatingStatusIds = ref(new Set())
```

### 3. 状态变更处理

```javascript
const handleStatusChange = (row, newStatus) => {
  const newStatusText = newStatus ? '生效' : '失效'
  
  ElMessageBox.confirm(
    `确定要将规则"${row.ruleName}"的状态改为"${newStatusText}"吗？`,
    '状态变更确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      beforeClose: (action, _instance, done) => {
        if (action === 'confirm') {
          updateRuleStatus(row, newStatusText)
          done()
        } else {
          ElMessage.info('已取消状态变更')
          done()
        }
      }
    }
  ).catch(() => {
    // 用户取消，开关自动回退
  })
}
```

### 4. 状态更新实现

```javascript
const updateRuleStatus = async (row, newStatus) => {
  // 添加到更新中的ID集合，禁用开关
  updatingStatusIds.value.add(row.id)
  
  try {
    // API调用
    await updateRuleStatusAPI({
      id: row.id,
      status: newStatus
    })
    
    // 更新本地数据
    const index = tableData.value.findIndex(item => item.id === row.id)
    if (index !== -1) {
      tableData.value[index].status = newStatus
    }
    
    ElMessage.success(`规则状态已更新为"${newStatus}"`)
    
  } catch (error) {
    ElMessage.error('状态更新失败：' + error.message)
    getList() // 刷新数据确保一致性
  } finally {
    // 重新启用开关
    updatingStatusIds.value.delete(row.id)
  }
}
```

## 用户交互流程

### 正常流程
1. 用户点击状态开关
2. 显示确认对话框
3. 用户点击"确定"
4. 开关被禁用，显示加载状态
5. API调用成功，更新本地数据
6. 显示成功提示，重新启用开关

### 取消流程
1. 用户点击状态开关
2. 显示确认对话框
3. 用户点击"取消"或按ESC键
4. 显示取消提示
5. 开关自动回退到原始状态

### 错误处理流程
1. 用户点击状态开关并确认
2. 开关被禁用
3. API调用失败
4. 显示错误提示
5. 刷新列表数据确保一致性
6. 重新启用开关

## 样式定制

```scss
// 状态开关样式优化
:deep(.el-switch) {
  .el-switch__label {
    font-size: 12px;
    font-weight: 500;
  }
  
  &.is-checked .el-switch__label--left {
    color: #67c23a;
  }
  
  &:not(.is-checked) .el-switch__label--right {
    color: #f56c6c;
  }
}
```

## 配置选项

### 开关属性
- `active-text`: 开启状态的文本（"生效"）
- `inactive-text`: 关闭状态的文本（"失效"）
- `active-color`: 开启状态的颜色（#67c23a）
- `inactive-color`: 关闭状态的颜色（#f56c6c）
- `inline-prompt`: 在开关内显示文本
- `disabled`: 禁用状态，防止重复操作

### 确认对话框属性
- `confirmButtonText`: 确认按钮文本
- `cancelButtonText`: 取消按钮文本
- `type`: 对话框类型（warning）
- `beforeClose`: 关闭前的回调处理

## 注意事项

1. **数据一致性**：确保本地数据与服务器数据保持一致
2. **错误处理**：API调用失败时要有适当的错误提示和数据恢复
3. **用户体验**：更新过程中禁用开关，防止重复操作
4. **状态回退**：用户取消时，开关会自动回退到原始状态
5. **加载提示**：长时间的API调用应该有加载提示

## 扩展功能

### 批量状态变更
可以扩展为支持批量选择和批量状态变更：

```javascript
const handleBatchStatusChange = (selectedRows, newStatus) => {
  // 批量状态变更逻辑
}
```

### 权限控制
可以根据用户权限控制开关的可用性：

```vue
<el-switch
  :disabled="!hasPermission || updatingStatusIds.has(scope.row.id)"
  @change="(value) => handleStatusChange(scope.row, value)"
/>
```

### 状态历史记录
可以记录状态变更的历史：

```javascript
const statusHistory = ref([])

const recordStatusChange = (row, oldStatus, newStatus) => {
  statusHistory.value.push({
    id: row.id,
    ruleName: row.ruleName,
    oldStatus,
    newStatus,
    timestamp: new Date(),
    operator: currentUser.name
  })
}
```

这个状态开关功能提供了完整的用户交互体验，包括确认、取消、加载状态和错误处理，确保了数据的一致性和用户操作的安全性。
