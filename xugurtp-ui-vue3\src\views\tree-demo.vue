<template>
  <div class="app-container">
    <SplitPanes>
      <template #left>
        <div class="left-panel">
          <!-- 左侧树形结构 -->
          <div class="tree-header">
            <span class="tree-title">目录结构</span>
          </div>
          <div class="tree-container">
            <el-tree
              ref="treeRef"
              :data="treeData"
              :props="treeProps"
              :expand-on-click-node="false"
              node-key="id"
              highlight-current
              @node-click="handleNodeClick"
            >
              <template #default="{ data }">
                <div class="tree-node">
                  <el-icon v-if="data.showType === 'CATALOG'" class="tree-icon">
                    <Folder />
                  </el-icon>
                  <el-icon v-else-if="data.showType === 'BUSINESS'" class="tree-icon">
                    <FolderOpened />
                  </el-icon>
                  <el-icon v-else class="tree-icon">
                    <Document />
                  </el-icon>
                  <span class="tree-label">{{ data.name }}</span>
                </div>
              </template>
            </el-tree>
          </div>
        </div>
      </template>
      
      <template #right>
        <div class="right-panel">
          <!-- 右侧内容显示区域 -->
          <div v-if="!selectedNode" class="empty-content">
            <el-empty description="请选择左侧节点查看详情" />
          </div>
          <div v-else class="content-area">
            <div class="content-header">
              <h3>{{ selectedNode.name }}</h3>
              <el-tag :type="getTagType(selectedNode.showType)">
                {{ getShowTypeText(selectedNode.showType) }}
              </el-tag>
            </div>
            <div class="content-body">
              <el-descriptions :column="2" border>
                <el-descriptions-item label="ID">{{ selectedNode.id }}</el-descriptions-item>
                <el-descriptions-item label="名称">{{ selectedNode.name }}</el-descriptions-item>
                <el-descriptions-item label="搜索名称">{{ selectedNode.searchName }}</el-descriptions-item>
                <el-descriptions-item label="显示类型">{{ getShowTypeText(selectedNode.showType) }}</el-descriptions-item>
                <el-descriptions-item v-if="selectedNode.businessId" label="业务ID">{{ selectedNode.businessId }}</el-descriptions-item>
              </el-descriptions>
              
              <!-- 根据不同类型显示不同内容 -->
              <div class="type-specific-content">
                <div v-if="selectedNode.showType === 'CATALOG'" class="catalog-content">
                  <h4>目录信息</h4>
                  <p>这是一个目录节点，包含子业务模块。</p>
                </div>
                <div v-else-if="selectedNode.showType === 'BUSINESS'" class="business-content">
                  <h4>业务模块信息</h4>
                  <p>业务ID: {{ selectedNode.businessId }}</p>
                  <p>这是一个业务模块，包含具体的查询功能。</p>
                </div>
                <div v-else-if="selectedNode.showType === 'QUERY'" class="query-content">
                  <h4>查询功能</h4>
                  <p>这是一个具体的查询功能模块。</p>
                  <el-button type="primary" @click="executeQuery">执行查询</el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </SplitPanes>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Folder, FolderOpened, Document } from '@element-plus/icons-vue'
import SplitPanes from '@/components/SplitPanes/index.vue'

// 树形数据
const treeData = ref([
  {
    id: "1",
    name: '数据管理',
    searchName: '数据管理',
    showType: 'CATALOG',
    children: [
      {
        id: "1-1",
        businessId: "1",
        name: '主数据管理',
        searchName: '主数据管理',
        showType: 'BUSINESS',
        children: [
          {
            id: "1-1-1",
            businessId: "1",
            name: '主数据查询',
            searchName: '主数据查询',
            showType: 'QUERY',
          },
          {
            id: "1-1-2",
            businessId: "1",
            name: '主数据维护',
            searchName: '主数据维护',
            showType: 'QUERY',
          }
        ]
      },
      {
        id: "1-2",
        businessId: "2",
        name: '元数据管理',
        searchName: '元数据管理',
        showType: 'BUSINESS',
        children: [
          {
            id: "1-2-1",
            businessId: "2",
            name: '元数据查询',
            searchName: '元数据查询',
            showType: 'QUERY',
          },
          {
            id: "1-2-2",
            businessId: "2",
            name: '元数据同步',
            searchName: '元数据同步',
            showType: 'QUERY',
          }
        ]
      }
    ]
  },
  {
    id: "2",
    name: '系统管理',
    searchName: '系统管理',
    showType: 'CATALOG',
    children: [
      {
        id: "2-1",
        businessId: "3",
        name: '用户管理',
        searchName: '用户管理',
        showType: 'BUSINESS',
        children: [
          {
            id: "2-1-1",
            businessId: "3",
            name: '用户列表',
            searchName: '用户列表',
            showType: 'QUERY',
          },
          {
            id: "2-1-2",
            businessId: "3",
            name: '角色管理',
            searchName: '角色管理',
            showType: 'QUERY',
          }
        ]
      }
    ]
  }
])

// 树形组件配置
const treeProps = {
  children: 'children',
  label: 'name'
}

// 当前选中的节点
const selectedNode = ref(null)
const treeRef = ref(null)

// 处理节点点击事件
const handleNodeClick = (data) => {
  selectedNode.value = data
  console.log('选中节点:', data)
}

// 获取标签类型
const getTagType = (showType) => {
  switch (showType) {
    case 'CATALOG':
      return 'primary'
    case 'BUSINESS':
      return 'success'
    case 'QUERY':
      return 'warning'
    default:
      return 'info'
  }
}

// 获取显示类型文本
const getShowTypeText = (showType) => {
  switch (showType) {
    case 'CATALOG':
      return '目录'
    case 'BUSINESS':
      return '业务模块'
    case 'QUERY':
      return '查询功能'
    default:
      return '未知'
  }
}

// 执行查询
const executeQuery = () => {
  ElMessage.success('查询功能执行成功！')
}
</script>

<style lang="scss" scoped>
.app-container {
  height: 100vh;
  background-color: #f5f5f5;
}

.left-panel {
  height: 100%;
  background-color: #fff;
  border-right: 1px solid #e4e7ed;
}

.tree-header {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
  background-color: #fafafa;
}

.tree-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.tree-container {
  padding: 16px;
  height: calc(100% - 60px);
  overflow-y: auto;
}

.tree-node {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.tree-icon {
  color: #409eff;
}

.tree-label {
  font-size: 14px;
  color: #606266;
}

.right-panel {
  height: 100%;
  background-color: #fff;
  overflow-y: auto;
}

.empty-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.content-area {
  padding: 24px;
}

.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.content-header h3 {
  margin: 0;
  color: #303133;
}

.content-body {
  .type-specific-content {
    margin-top: 24px;
    padding: 16px;
    background-color: #f9f9f9;
    border-radius: 4px;
    
    h4 {
      margin: 0 0 12px 0;
      color: #409eff;
    }
    
    p {
      margin: 8px 0;
      color: #606266;
    }
  }
}

:deep(.el-tree-node__content) {
  height: 40px;
  
  &:hover {
    background-color: #f5f7fa;
  }
}

:deep(.el-tree-node__content.is-current) {
  background-color: #e6f7ff;
  color: #409eff;
}
</style>
