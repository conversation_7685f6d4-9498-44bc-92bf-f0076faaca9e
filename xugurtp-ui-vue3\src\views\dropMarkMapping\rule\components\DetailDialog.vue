<template>
  <el-dialog
    v-model="visible"
    :title="config.title || '详情'"
    :width="config.width || '1400px'"
    :draggable="true"
    class="detail-dialog"
  >
    <div class="dialog-content">
      <!-- 统一的内容结构 -->
      <div class="section">
        <div class="section-header">
          <el-icon class="section-icon"><Document /></el-icon>
          <span class="section-title">{{ config.sectionTitle || '明细信息' }}</span>
        </div>

        <!-- 规则名称 -->
        <div class="rule-name-section">
          <div class="rule-name-header">
            <el-icon class="rule-icon"><Setting /></el-icon>
            <span class="rule-name">{{ data.ruleName || '规则名称' }}</span>
          </div>

          <!-- 基础信息 -->
          <el-descriptions
            class="basic-info-descriptions"
            :column="2"
            size="default"
          >
            <el-descriptions-item
              v-for="(card, index) in infoCards"
              :key="index"
              :label="card.label"
              :span="1"
            >
              <span :class="{ 'link-text': card.isLink }">
                {{ card.value }}
              </span>
            </el-descriptions-item>
          </el-descriptions>

          <!-- 明细表格 -->
          <div class="detail-table">
            <el-table
              :data="data.tableData || []"
              :header-cell-class-name="addHeaderCellClassName"
              empty-text="暂无数据"
              @row-click="handleRowClick"
              row-key="id"
              :expand-row-keys="expandedRows"
              @expand-change="handleExpandChange"
            >
              <el-table-column type="expand" width="30">
                <template #default="{ row }">
                  <div class="expand-content">
                    <!-- 规则明细展开内容 -->
                    <div v-if="config.type === 'rule'">
                      <div class="expand-section">
                        <div class="expand-title">规则详情</div>
                        <div class="expand-grid">
                          <div class="expand-item">
                            <span class="expand-label">标准值:</span>
                            <span class="expand-value">{{ row.standardValue }}</span>
                          </div>
                          <div class="expand-item">
                            <span class="expand-label">对象值:</span>
                            <span class="expand-value">{{ row.objectValue }}</span>
                          </div>
                        </div>
                      </div>

                      <div class="expand-section">
                        <div class="expand-title">评估详情</div>
                        <div class="expand-grid">
                          <div class="expand-item">
                            <span class="expand-label">标准值:</span>
                            <span class="expand-value">{{ row.evaluationStandardValue || '不为空' }}</span>
                          </div>
                          <div class="expand-item">
                            <span class="expand-label">对象值:</span>
                            <span class="expand-value">{{ row.evaluationObjectValue || '有空值' }}</span>
                          </div>
                        </div>
                      </div>

                      <div class="expand-section">
                        <div class="expand-title">规则类型</div>
                        <div class="expand-grid">
                          <div class="expand-item full-width">
                            <span class="expand-label">{{ row.ruleType }}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- 执行记录展开内容 -->
                    <div v-else>
                      <div class="expand-section">
                        <div class="expand-title">有效映射关系表（{{ row.validMappingCount }}条）</div>
                        <div class="mapping-grid">
                          <div
                            v-for="(mapping, index) in row.mappingRelations"
                            :key="index"
                            class="mapping-item"
                          >
                            <div class="mapping-row">
                              <span class="mapping-label">映射元数据编码：</span>
                              <span class="mapping-value">{{ mapping.metadataCode }}</span>
                            </div>
                            <div class="mapping-row">
                              <span class="mapping-label">映射元数据编码：</span>
                              <span class="mapping-value">{{ mapping.metadataCode }}</span>
                            </div>
                            <div class="mapping-row">
                              <span class="mapping-label">元数据路径：</span>
                              <span class="mapping-value">{{ mapping.metadataPath }}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
              </el-table-column>

              <!-- 动态渲染表格列 -->
              <el-table-column
                v-for="column in tableColumns"
                :key="column.prop"
                :type="column.type"
                :prop="column.prop"
                :label="column.label"
                :width="column.width"
                :min-width="column.minWidth"
                :align="column.align"
                :fixed="column.fixed"
              >
                <template #default="{ row }" v-if="column.slot">
                  <!-- 评估结果标签 -->
                  <el-tag
                    v-if="column.slot === 'EvaluationResultTag'"
                    :type="row.evaluationResult === '通过' ? 'success' : 'danger'"
                    size="small"
                  >
                    {{ row.evaluationResult }}
                  </el-tag>

                  <!-- 评估详情 -->
                  <div
                    v-else-if="column.slot === 'EvaluationDetail'"
                    class="evaluation-detail"
                  >
                    <div>标准值: {{ row.standardValue }}</div>
                    <div>对象值: {{ row.objectValue }}</div>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="visible = false">{{ config.closeText || '关闭' }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Document, Setting } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  },
  config: {
    type: Object,
    default: () => ({
      type: 'rule', // 类型：'rule' | 'execute'
      title: '详情',
      width: '1400px',
      closeText: '关闭',
      sectionTitle: '明细信息'
    })
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 展开的行
const expandedRows = ref([])

// 基础信息卡片配置
const infoCards = computed(() => {
  const basicInfo = props.data.basicInfo || {}

  if (props.config.type === 'rule') {
    // 规则明细类型
    return [
      { label: '规则名称', value: basicInfo.ruleName || '规则名称' },
      { label: '数据标准', value: basicInfo.dataStandard || '数据标准' },
      { label: '标准集', value: basicInfo.standardSet || '基础标准集', isLink: true },
      { label: '映射对象', value: basicInfo.mappingObject || 'dws_dddd_dddddd' }
    ]
  } else {
    // 执行记录类型
    return [
      { label: '规则名称', value: basicInfo.ruleName || '金融标准映射规则' },
      { label: '映射数据集/对象', value: basicInfo.mappingDataset || '基础标准集（公示标准版/基础）：性别_姓名_员工ID' },
      { label: '开始执行时间', value: basicInfo.startTime || '2025-05-05 10:47:44' },
      { label: '所属资产', value: basicInfo.belongAsset || 'dws_dddd_dddddd' }
    ]
  }
})

// 表格列配置
const tableColumns = computed(() => {
  if (props.config.type === 'rule') {
    // 规则明细表格列
    return [
      { prop: 'standardAttribute', label: '关联标准属性', minWidth: 120 },
      {
        prop: 'evaluationResult',
        label: '评估结果',
        minWidth: 100,
        align: 'center',
        slot: 'EvaluationResultTag'
      },
      {
        prop: 'evaluationDetail',
        label: '评估详情',
        minWidth: 200,
        slot: 'EvaluationDetail'
      },
      { prop: 'ruleType', label: '规则类型', minWidth: 100, align: 'center' }
    ]
  } else {
    // 执行记录表格列
    return [
      { type: 'index', label: '序号', width: 60 },
      { prop: 'standardChineseName', label: '标准中文名称', minWidth: 120 },
      { prop: 'standardCode', label: '标准编码', minWidth: 120 },
      { prop: 'validMappingCount', label: '有效映射关系数', minWidth: 140, align: 'center' }
    ]
  }
})

// 移除不再使用的expandContentComponent

// 方法
const addHeaderCellClassName = () => 'table-header-cell'

/**
 * 处理行点击事件
 * @param {Object} row 点击的行数据
 */
const handleRowClick = (row) => {
  // 切换展开状态
  const index = expandedRows.value.indexOf(row.id)
  if (index > -1) {
    expandedRows.value.splice(index, 1)
  } else {
    expandedRows.value.push(row.id)
  }
}

/**
 * 处理展开变化事件
 * @param {Object} row 行数据
 * @param {Array} expandedRowsData 展开的行数据
 */
const handleExpandChange = (row, expandedRowsData) => {
  // 同步展开状态
  expandedRows.value = expandedRowsData.map(item => item.id)
}

// 监听visible变化，打开时重置状态
watch(visible, (newVal) => {
  if (newVal) {
    expandedRows.value = []
  }
})
</script>

<style lang="scss" scoped>
.detail-dialog {
  :deep(.el-dialog__body) {
    padding: 20px;
  }

  .dialog-content {
    .section {
      .section-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 12px;
        border-bottom: 2px solid #e4e7ed;

        .section-icon {
          margin-right: 8px;
          color: #409eff;
          font-size: 18px;
        }

        .section-title {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
        }
      }

      .rule-name-section {
        .rule-name-header {
          display: flex;
          align-items: center;
          margin-bottom: 20px;

          .rule-icon {
            margin-right: 8px;
            color: #67c23a;
            font-size: 16px;
          }

          .rule-name {
            font-size: 14px;
            font-weight: 600;
            color: #303133;
          }
        }

        .basic-info-descriptions {
          margin-bottom: 20px;

          :deep(.el-descriptions__table) {
            // .el-descriptions__label {
            //   font-weight: 600;
            //   color: #606266;
            //   background-color: #fafafa;
            //   width: 120px;
            //   text-align: right;
            //   padding-right: 12px;
            // }

            .el-descriptions__content {
              color: #303133;
              padding-left: 12px;

              .link-text {
                color: #409eff;
                cursor: pointer;
                font-weight: 500;

                &:hover {
                  text-decoration: underline;
                }
              }
            }
          }

          // 响应式布局
          @media (max-width: 1200px) {
            :deep(.el-descriptions__table) {
              .el-descriptions__label {
                width: 100px;
              }
            }
          }

          @media (max-width: 768px) {
            :deep(.el-descriptions__table) {
              .el-descriptions__label {
                width: 80px;
                font-size: 12px;
              }

              .el-descriptions__content {
                font-size: 12px;
              }
            }
          }
        }

        .detail-table {
          .evaluation-detail {
            font-size: 12px;
            line-height: 1.4;

            div {
              margin-bottom: 2px;

              &:last-child {
                margin-bottom: 0;
              }
            }
          }
        }
      }
    }
  }
}

// 展开内容样式
:deep(.el-table__expanded-cell) {
  padding: 20px !important;
  background-color: #fafbfc;

  .expand-content {
    .expand-section {
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      .expand-title {
        font-size: 14px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid #e4e7ed;
      }

      .expand-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;

        .expand-item {
          display: flex;
          align-items: center;

          &.full-width {
            grid-column: 1 / -1;
          }

          .expand-label {
            font-size: 12px;
            color: #909399;
            margin-right: 8px;
            min-width: 60px;
          }

          .expand-value {
            font-size: 12px;
            color: #303133;
          }
        }
      }

      .mapping-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 16px;

        .mapping-item {
          padding: 12px;
          background: #fff;
          border: 1px solid #e4e7ed;
          border-radius: 6px;

          .mapping-row {
            display: flex;
            margin-bottom: 8px;

            &:last-child {
              margin-bottom: 0;
            }

            .mapping-label {
              font-size: 12px;
              color: #909399;
              margin-right: 8px;
              min-width: 80px;
            }

            .mapping-value {
              font-size: 12px;
              color: #303133;
              flex: 1;
            }
          }
        }
      }
    }
  }
}

:deep(.el-table) {
  .table-header-cell {
    background-color: #f5f7fa;
    color: #606266;
    font-weight: 600;
  }

  .el-table__row {
    cursor: pointer;

    &:hover {
      background-color: #f5f7fa;
    }
  }
}
</style>
